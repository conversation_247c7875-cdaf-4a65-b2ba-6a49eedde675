# 客户管理权限控制修复说明

## 问题描述

用户反馈：普通员工不应该显示"所有客户"页面，只有部门负责人才应该看到"所有客户"tab。

## 解决方案

### 1. 后端实现

#### 1.1 添加检查部门负责人的服务方法

**文件**：`back/module_customer/service/customer_service.py`

```python
@classmethod
async def check_user_is_dept_leader_services(cls, db: AsyncSession, current_user: CurrentUserModel):
    """
    检查当前用户是否为部门负责人

    :param db: orm对象
    :param current_user: 当前用户
    :return: 是否为部门负责人
    """
    current_user_id = current_user.user.user_id
    
    # 查找当前用户作为负责人的所有部门
    leader_depts_query = select(SysDept).where(
        SysDept.leader == current_user_id,
        SysDept.del_flag == '0'
    )
    leader_depts_result = await db.execute(leader_depts_query)
    leader_depts = leader_depts_result.scalars().all()

    # 如果用户是任何部门的负责人，返回True
    is_dept_leader = len(leader_depts) > 0
    
    return {
        'isDeptLeader': is_dept_leader,
        'leaderDeptCount': len(leader_depts),
        'leaderDeptIds': [dept.dept_id for dept in leader_depts]
    }
```

#### 1.2 添加API接口

**文件**：`back/module_customer/controller/customer_controller.py`

```python
@router.get('/check-dept-leader', summary='检查当前用户是否为部门负责人')
async def check_dept_leader(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    检查当前用户是否为部门负责人

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 是否为部门负责人
    """
    result = await CustomerService.check_user_is_dept_leader_services(db, current_user)
    return ResponseUtil.success(data=result)
```

### 2. 前端实现

#### 2.1 添加API调用函数

**文件**：`front/src/api/customer/index.js`

```javascript
// 检查当前用户是否为部门负责人
export function checkDeptLeader() {
  return request({
    url: '/customer/check-dept-leader',
    method: 'get'
  })
}
```

#### 2.2 修改前端组件

**文件**：`front/src/views/customer/index.vue`

1. **导入API函数**：
```javascript
import {
  // ... 其他导入
  checkDeptLeader,
} from "@/api/customer/index";
```

2. **添加检查部门负责人的方法**：
```javascript
/** 检查当前用户是否为部门负责人 */
function checkUserDeptLeader() {
  checkDeptLeader().then(response => {
    console.log("检查部门负责人响应:", response);
    if (response.data && response.data.isDeptLeader !== undefined) {
      showAllCustomersTab.value = response.data.isDeptLeader;
      console.log(`用户是否为部门负责人: ${response.data.isDeptLeader}`);
      
      // 如果用户不是部门负责人，且当前tab是"所有客户"，则切换到"我的客户"
      if (!response.data.isDeptLeader && activeTab.value === "all-customers") {
        activeTab.value = "my-customers";
      }
    } else {
      // 默认不显示"所有客户"tab
      showAllCustomersTab.value = false;
      console.warn("检查部门负责人响应数据格式不正确:", response.data);
    }
  }).catch(error => {
    // 出错时默认不显示"所有客户"tab
    showAllCustomersTab.value = false;
    console.error("检查部门负责人失败:", error);
  });
}
```

3. **在组件挂载时调用**：
```javascript
// 组件挂载时的初始化
checkUserDeptLeader();
getCustomerTree();
getList();
```

4. **模板中的条件渲染**：
```vue
<el-tab-pane label="所有客户" name="all-customers" v-if="showAllCustomersTab"></el-tab-pane>
```

## 权限控制逻辑

### 判断标准
- **部门负责人**：在 `sys_dept` 表中，`leader` 字段等于当前用户ID的部门
- **普通员工**：不是任何部门的负责人

### 显示规则
- ✅ **部门负责人**：显示"我的客户"和"所有客户"两个tab
- ❌ **普通员工**：只显示"我的客户"tab

### 数据权限
- **我的客户**：显示当前用户作为内部负责人的客户
- **所有客户**：显示当前用户作为负责人的所有部门下的员工负责的客户

## 测试结果

### 测试环境数据
- **用户1（超级管理员）**：负责9个部门 → 显示"所有客户"tab ✅
- **用户2（年糕）**：负责1个部门 → 显示"所有客户"tab ✅
- **普通员工**：不负责任何部门 → 不显示"所有客户"tab ✅

### API响应格式
```json
{
  "isDeptLeader": true,
  "leaderDeptCount": 9,
  "leaderDeptIds": [100, 101, 102, 103, 104, 105, 107, 108, 109]
}
```

## 安全性考虑

1. **后端验证**：权限检查在后端进行，前端只是根据结果控制显示
2. **API保护**：所有客户相关API都需要登录认证
3. **数据隔离**：不同tab调用不同的API，确保数据权限正确

## 部署说明

1. **后端**：部署修改后的服务代码
2. **前端**：重新构建并部署前端代码
3. **数据库**：无需修改数据库结构

## 验证方法

1. 使用部门负责人账号登录，验证能看到"所有客户"tab
2. 使用普通员工账号登录，验证只能看到"我的客户"tab
3. 检查浏览器控制台日志，确认权限检查正常执行

## 相关文件

### 后端文件
- `back/module_customer/service/customer_service.py`
- `back/module_customer/controller/customer_controller.py`

### 前端文件
- `front/src/api/customer/index.js`
- `front/src/views/customer/index.vue`

## 注意事项

1. 权限检查在每次进入客户管理页面时执行
2. 如果API调用失败，默认不显示"所有客户"tab，确保安全性
3. 部门负责人信息变更后，需要重新进入页面才能看到权限变化
