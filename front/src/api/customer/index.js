import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询客户列表
export function listCustomer(query) {
  return request({
    url: '/customer/list',
    method: 'get',
    params: query
  })
}

// 查询客户树形结构
export function treeCustomer() {
  return request({
    url: '/customer/tree',
    method: 'get'
  })
}

// 查询客户分页列表
export function pageCustomer(query) {
  return request({
    url: '/customer/page',
    method: 'get',
    params: query
  })
}

// 查询所有客户分页列表（下属员工负责的客户）
export function pageAllCustomers(query) {
  return request({
    url: '/customer/all-customers/page',
    method: 'get',
    params: query
  })
}

// 查询我的客户分页列表
export function pageMyCustomers(query) {
  return request({
    url: '/customer/my-customers/page',
    method: 'get',
    params: query
  })
}

// 查询客户详细
export function getCustomer(customerId) {
  return request({
    url: '/customer/' + parseStrEmpty(customerId),
    method: 'get'
  })
}

// 新增客户
export function addCustomer(data) {
  return request({
    url: '/customer',
    method: 'post',
    data: data
  })
}

// 修改客户
export function updateCustomer(data) {
  return request({
    url: '/customer',
    method: 'put',
    data: data
  })
}

// 删除客户
export function delCustomer(customerId) {
  return request({
    url: '/customer/' + customerId,
    method: 'delete'
  })
}

// 校验客户名称是否唯一
export function checkCustomerNameUnique(customerName, customerId) {
  return request({
    url: '/customer/check/' + customerName,
    method: 'get',
    params: {
      customerId: customerId
    }
  })
}
