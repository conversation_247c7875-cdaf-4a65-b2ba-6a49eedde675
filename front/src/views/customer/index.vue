<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="appStore.device === 'mobile'"
        class="default-theme"
      >
        <!--集团树结构-->
        <pane size="16">
          <el-col>
            <div class="head-container">
              <el-row :gutter="10">
                <el-col :span="18">
                  <el-input
                    v-model="customerName"
                    placeholder="请输入集团名称"
                    clearable
                    prefix-icon="Search"
                    style="margin-bottom: 20px"
                  />
                </el-col>
                <el-col :span="6">
                  <el-button type="primary" plain @click="handleViewAll">重置</el-button>
                </el-col>
              </el-row>
            </div>
            <div class="head-container">
              <el-tree
                :data="customerOptions"
                :props="{ label: 'customerName', children: 'children' }"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="customerTreeRef"
                node-key="customerId"
                highlight-current
                default-expand-all
                @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node">
                    <span>
                      <el-icon v-if="data.customerLevel === '1'"><Folder /></el-icon>
                      <el-icon v-else><OfficeBuilding /></el-icon>
                      {{ node.label }}
                    </span>
                    <span class="tree-node-count" v-if="data.customerLevel === '1' && data.children && data.children.length > 0">
                      ({{ data.children.length }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
          </el-col>
        </pane>
        <!--客户数据-->
        <pane size="84">
          <el-col>
            <!-- Tab切换 -->
            <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="customer-tabs">
              <el-tab-pane label="我的客户" name="my-customers"></el-tab-pane>
              <el-tab-pane label="所有客户" name="all-customers" v-if="showAllCustomersTab"></el-tab-pane>
            </el-tabs>

            <el-form
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              v-show="showSearch"
              label-width="68px"
            >
              <el-form-item label="客户名称" prop="customerName">
                <el-input
                  v-model="queryParams.customerName"
                  placeholder="请输入客户名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="客户类型" prop="customerType">
                <el-select
                  v-model="queryParams.customerType"
                  placeholder="客户类型"
                  clearable
                  style="width: 240px"
                >
                  <el-option label="政府" value="政府" />
                  <el-option label="企业" value="企业" />
                  <el-option label="园区" value="园区" />
                </el-select>
              </el-form-item>
              <el-form-item label="客户来源" prop="customerSource">
                <el-select
                  v-model="queryParams.customerSource"
                  placeholder="客户来源"
                  clearable
                  style="width: 240px"
                >
                  <el-option label="自主开发" value="自主开发" />
                  <el-option label="合作伙伴" value="合作伙伴" />
                  <el-option label="客户推荐" value="客户推荐" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间" style="width: 308px">
                <el-date-picker
                  v-model="dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['customer:customer:add']"
                  >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="Edit"
                  :disabled="single"
                  @click="handleUpdate"
                  v-hasPermi="['customer:customer:edit']"
                  >修改</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  plain
                  icon="Delete"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['customer:customer:remove']"
                  >删除</el-button
                >
              </el-col>
              <right-toolbar
                v-model:showSearch="showSearch"
                @queryTable="getList"
                :columns="columns"
              ></right-toolbar>
            </el-row>

            <el-table
              v-loading="loading"
              :data="customerList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column
                label="客户编号"
                align="center"
                key="customerId"
                prop="customerId"
                v-if="columns[0].visible"
              />
              <el-table-column
                label="客户名称"
                align="center"
                key="customerName"
                prop="customerName"
                v-if="columns[1].visible"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="客户类型"
                align="center"
                key="customerType"
                prop="customerType"
                v-if="columns[2].visible"
              />
              <el-table-column
                label="客户来源"
                align="center"
                key="customerSource"
                prop="customerSource"
                v-if="columns[3].visible"
              />
              <el-table-column
                label="所在地区"
                align="center"
                key="area"
                v-if="columns[4].visible"
              >
                <template #default="scope">
                  <span>{{ scope.row.province }} {{ scope.row.city }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="状态"
                align="center"
                key="status"
                v-if="columns[5].visible"
              >
                <template #default="scope">
                  <el-switch
                    v-model="scope.row.status"
                    active-value="0"
                    inactive-value="1"
                    @change="handleStatusChange(scope.row)"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column
                label="客户重要性"
                align="center"
                key="customerImportance"
                prop="customerImportance"
                v-if="columns[6].visible"
              >
                <template #default="scope">
                  <el-tag
                    :type="scope.row.customerImportance === '重点客户' ? 'danger' :
                          scope.row.customerImportance === '普通客户' ? 'warning' : 'success'"
                  >
                    {{ scope.row.customerImportance }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                label="创建时间"
                align="center"
                prop="createTime"
                v-if="columns[7].visible"
                width="160"
              >
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="150"
                class-name="small-padding fixed-width"
              >
                <template #default="scope">
                  <el-tooltip content="查看" placement="top">
                    <el-button
                      link
                      type="primary"
                      icon="View"
                      @click="() => handleView(scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="修改" placement="top">
                    <el-button
                      link
                      type="primary"
                      icon="Edit"
                      @click="handleUpdate(scope.row)"
                      v-hasPermi="['customer:customer:edit']"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top">
                    <el-button
                      link
                      type="primary"
                      icon="Delete"
                      @click="handleDelete(scope.row)"
                      v-hasPermi="['customer:customer:remove']"
                    ></el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改客户对话框 -->
    <customer-form
      ref="customerFormRef"
      :title="title"
      :open="open"
      @cancel="cancel"
      @submit="submitForm"
    />

    <!-- 查看客户详情对话框 -->
    <customer-detail
      ref="customerDetailRef"
      :title="detailTitle"
      :open="openDetail"
      @cancel="cancelDetail"
    />
  </div>
</template>

<script setup name="Customer">
import useAppStore from "@/store/modules/app";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import CustomerForm from "./components/CustomerForm.vue";
import CustomerDetail from "./components/CustomerDetail.vue";
import {
  listCustomer,
  pageCustomer,
  pageAllCustomers,
  pageMyCustomers,
  getCustomer,
  addCustomer,
  updateCustomer,
  delCustomer,
  treeCustomer,
} from "@/api/customer/index";
import { Folder, OfficeBuilding } from '@element-plus/icons-vue';

const router = useRouter();
const appStore = useAppStore();
const { proxy } = getCurrentInstance();

const customerList = ref([]);
const open = ref(false);
const openDetail = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const detailTitle = ref("客户详情");
const dateRange = ref([]);
const customerName = ref("");
const customerOptions = ref([]);

// 用于取消请求的控制器
let currentRequestController = null;

// 列显隐信息
const columns = ref([
  { key: 0, label: `客户编号`, visible: true },
  { key: 1, label: `客户名称`, visible: true },
  { key: 2, label: `客户类型`, visible: true },
  { key: 3, label: `客户来源`, visible: true },
  { key: 4, label: `所在地区`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `客户重要性`, visible: true },
  { key: 7, label: `创建时间`, visible: true },
]);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerName: undefined,
    customerType: undefined,
    customerSource: undefined,
    parentId: undefined,
    customerId: undefined,
    customerLevel: undefined,
  },
  // 当前激活的tab
  activeTab: "my-customers",
  // 是否显示所有客户tab
  showAllCustomersTab: true,
});

const { queryParams, activeTab, showAllCustomersTab } = toRefs(data);

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.customerName.indexOf(value) !== -1;
};

/** 根据名称筛选客户树 */
watch(customerName, (val) => {
  if (proxy.$refs["customerTreeRef"]) {
    proxy.$refs["customerTreeRef"].filter(val);
  }
});

/** 查询客户列表 */
function getList() {
  // 取消之前的请求
  if (currentRequestController) {
    currentRequestController.abort();
  }

  // 创建新的请求控制器
  currentRequestController = new AbortController();

  loading.value = true;
  let apiCall;

  // 根据当前tab选择不同的API
  if (activeTab.value === "my-customers") {
    apiCall = pageMyCustomers;
  } else if (activeTab.value === "all-customers") {
    apiCall = pageAllCustomers;
  } else {
    apiCall = pageCustomer;
  }

  console.log(`正在调用API: ${activeTab.value === "my-customers" ? "pageMyCustomers" : activeTab.value === "all-customers" ? "pageAllCustomers" : "pageCustomer"}`);

  apiCall(proxy.addDateRange(queryParams.value, dateRange.value)).then(
    (res) => {
      // 检查请求是否被取消
      if (currentRequestController && !currentRequestController.signal.aborted) {
        loading.value = false;
        customerList.value = res.data?.rows || [];
        total.value = res.data?.total || 0;
        console.log(`API调用成功，获取到 ${res.data?.total || 0} 条数据`);
      }
    }
  ).catch((error) => {
    // 检查是否是请求取消导致的错误
    if (error.name !== 'AbortError') {
      loading.value = false;
      console.error('API调用失败:', error);
    }
  });
}

/** 处理Tab点击事件 */
function handleTabClick(tab) {
  console.log(`Tab切换到: ${tab.name || tab.paneName}`);

  // 重置查询参数
  queryParams.value.pageNum = 1;

  // 清除树形结构中的选中节点（切换tab时重置筛选）
  if (proxy.$refs.customerTreeRef) {
    proxy.$refs.customerTreeRef.setCurrentKey(null);
  }
  queryParams.value.customerId = undefined;
  queryParams.value.parentId = undefined;
  queryParams.value.customerLevel = undefined;

  // 延迟一点时间再加载数据，避免快速切换时的请求混乱
  setTimeout(() => {
    getList();
  }, 100);
}

/** 查询客户树结构 */
function getCustomerTree() {
  treeCustomer().then(response => {
    console.log("获取客户树形结构响应:", response);
    if (response.data && Array.isArray(response.data)) {
      // 过滤出只有集团级别的节点作为根节点
      const treeData = response.data.filter(node => node.customerLevel === '1').map(group => {
        if (!group.children) {
          group.children = [];
        }
        return group;
      });
      customerOptions.value = treeData;
    } else {
      customerOptions.value = [];
      console.error("获取客户树形结构数据格式不正确:", response.data);
    }
  }).catch(error => {
    console.error("获取客户树形结构失败:", error);
    proxy.$modal.msgError("获取客户树形结构失败");
  });
}

/** 节点单击事件 */
function handleNodeClick(data) {
  console.log("节点单击事件:", data);
  queryParams.value.customerId = data.customerId;
  handleQuery();
}

/** 查看全部客户按钮操作 */
function handleViewAll() {
  // 清除树形结构中的选中节点
  if (proxy.$refs.customerTreeRef) {
    proxy.$refs.customerTreeRef.setCurrentKey(null);
  }
  // 重置查询参数
  queryParams.value.customerId = undefined;
  queryParams.value.parentId = undefined;
  queryParams.value.customerLevel = undefined;
  // 重新查询数据
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.parentId = undefined;
  queryParams.value.customerId = undefined;
  queryParams.value.customerLevel = undefined;
  if (proxy.$refs.customerTreeRef) {
    proxy.$refs.customerTreeRef.setCurrentKey(null);
  }
  handleQuery();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const customerIds = row.customerId || ids.value;
  proxy.$modal
    .confirm('是否确认删除客户编号为"' + customerIds + '"的数据项？')
    .then(function () {
      return delCustomer(customerIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 客户状态修改  */
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.customerName + '"客户吗?')
    .then(function () {
      return updateCustomer({
        customerId: row.customerId,
        status: row.status
      });
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(function () {
      row.status = row.status === "0" ? "1" : "0";
    });
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.customerId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 取消按钮 */
function cancel() {
  open.value = false;
}

/** 取消详情按钮 */
function cancelDetail() {
  openDetail.value = false;
}

/** 查看按钮操作 */
function handleView(row) {
  const customerId = row.customerId;
  getCustomer(customerId).then((response) => {
    // 确保响应数据存在
    if (response.data) {
      // 使用新的方法设置表单数据
      proxy.$refs["customerDetailRef"].setFormData(response.data);
      openDetail.value = true;
    } else {
      proxy.$modal.msgError("获取客户详情失败");
    }
  });
}

/** 新增按钮操作 */
function handleAdd() {
  proxy.$refs["customerFormRef"].reset();
  open.value = true;
  title.value = "添加客户";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const customerId = row.customerId || ids.value;
  getCustomer(customerId).then((response) => {
    // 确保响应数据存在
    if (response.data) {
      // 调试输出
      console.log("获取到的客户详情数据:", response.data);

      // 使用新的方法设置表单数据
      proxy.$refs["customerFormRef"].setFormData(response.data);

      open.value = true;
      title.value = "修改客户";
    } else {
      proxy.$modal.msgError("获取客户详情失败");
    }
  });
}

/** 提交按钮 */
function submitForm(formData) {
  if (formData.customerId != undefined) {
    updateCustomer(formData).then(response => {
      proxy.$modal.msgSuccess("修改成功");
      open.value = false;
      getList();
    });
  } else {
    addCustomer(formData).then(response => {
      proxy.$modal.msgSuccess("新增成功");
      open.value = false;
      getList();
    });
  }
}

getCustomerTree();
getList();
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-node-count {
  color: #909399;
  font-size: 12px;
}

.el-icon {
  margin-right: 5px;
  color: #409EFF;
}

.customer-tabs {
  margin-bottom: 15px;
}
</style>
