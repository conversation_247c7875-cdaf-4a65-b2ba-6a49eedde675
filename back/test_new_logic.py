#!/usr/bin/env python3
"""
测试修改后的客户管理逻辑
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select
from config.database import AsyncSessionLocal
from module_admin.entity.do.dept_do import SysDept
from module_admin.entity.do.user_do import SysUser
from module_customer.service.customer_service import CustomerService


async def test_new_logic():
    """测试新的客户管理逻辑"""
    
    async with AsyncSessionLocal() as db:
        print("=== 测试修改后的客户管理逻辑 ===\n")
        
        # 1. 查看所有用户和部门数据
        print("1. 查看所有用户和部门数据:")
        
        # 查询所有用户
        all_users_query = select(SysUser).where(SysUser.del_flag == '0')
        all_users_result = await db.execute(all_users_query)
        all_users = all_users_result.scalars().all()
        
        print(f"  系统中总用户数: {len(all_users)}")
        for user in all_users:
            print(f"    用户ID: {user.user_id}, 姓名: {user.nick_name}, 部门ID: {user.dept_id}")
        
        # 查询所有部门
        all_depts_query = select(SysDept).where(SysDept.del_flag == '0')
        all_depts_result = await db.execute(all_depts_query)
        all_depts = all_depts_result.scalars().all()
        
        print(f"\n  系统中总部门数: {len(all_depts)}")
        for dept in all_depts:
            print(f"    部门ID: {dept.dept_id}, 部门名称: {dept.dept_name}, 负责人ID: {dept.leader}")
        
        print("\n" + "="*50 + "\n")
        
        # 2. 测试用户1（超级管理员）的新逻辑
        print("2. 测试用户1（超级管理员）的新逻辑:")
        test_user_id = 1
        
        # 查找用户1作为负责人的所有部门
        leader_depts_query = select(SysDept).where(
            SysDept.leader == test_user_id,
            SysDept.del_flag == '0'
        )
        leader_depts_result = await db.execute(leader_depts_query)
        leader_depts = leader_depts_result.scalars().all()
        
        print(f"  用户 {test_user_id} 作为负责人的部门:")
        for dept in leader_depts:
            print(f"    部门ID: {dept.dept_id}, 部门名称: {dept.dept_name}")
        
        # 调用新的方法
        subordinate_ids = await CustomerService.get_subordinate_user_ids(db, test_user_id)
        print(f"\n  根据新逻辑获取的员工ID列表: {subordinate_ids}")
        
        # 3. 测试用户2的逻辑
        print(f"\n3. 测试用户2的逻辑:")
        test_user_id_2 = 2
        
        # 查找用户2作为负责人的所有部门
        leader_depts_query_2 = select(SysDept).where(
            SysDept.leader == test_user_id_2,
            SysDept.del_flag == '0'
        )
        leader_depts_result_2 = await db.execute(leader_depts_query_2)
        leader_depts_2 = leader_depts_result_2.scalars().all()
        
        print(f"  用户 {test_user_id_2} 作为负责人的部门:")
        for dept in leader_depts_2:
            print(f"    部门ID: {dept.dept_id}, 部门名称: {dept.dept_name}")
        
        # 调用新的方法
        subordinate_ids_2 = await CustomerService.get_subordinate_user_ids(db, test_user_id_2)
        print(f"\n  根据新逻辑获取的员工ID列表: {subordinate_ids_2}")
        
        # 4. 查看客户负责人数据
        print(f"\n4. 查看客户负责人数据:")
        from module_customer.entity.do.customer_do import CustomerInternalManager, Customer
        
        managers_query = select(CustomerInternalManager, Customer, SysUser).where(
            CustomerInternalManager.customer_id == Customer.customer_id,
            CustomerInternalManager.user_id == SysUser.user_id,
            Customer.del_flag == '0'
        )
        managers_result = await db.execute(managers_query)
        managers = managers_result.all()
        
        print(f"  客户负责人关联数据 (总数: {len(managers)}):")
        for manager, customer, user in managers:
            print(f"    客户: {customer.customer_name}, 负责人: {user.nick_name}(ID:{user.user_id})")
        
        print("\n" + "="*50)
        print("✅ 测试完成！")
        
        print(f"\n总结:")
        print(f"- 用户1（超级管理员）作为负责人的部门数: {len(leader_depts)}")
        print(f"- 用户1管理的所有员工数: {len(subordinate_ids)}")
        print(f"- 用户2作为负责人的部门数: {len(leader_depts_2)}")
        print(f"- 用户2管理的所有员工数: {len(subordinate_ids_2)}")
        print(f"- 系统中客户负责人关联数: {len(managers)}")


if __name__ == "__main__":
    asyncio.run(test_new_logic())
