from datetime import datetime
from fastapi import Request
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Union, Dict, Any
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.do.user_do import SysUser
from module_admin.entity.do.dept_do import SysDept
from module_customer.dao.customer_dao import CustomerDao
from module_customer.entity.do.customer_do import Customer, CustomerContact, CustomerInternalManager
from module_customer.entity.vo.customer_vo import (
    AddCustomerModel,
    CustomerContactModel,
    CustomerInternalManagerModel,
    CustomerModel,
    CustomerPageQueryModel,
    CustomerQueryModel,
    EditCustomerModel,
)
from utils.common_util import CamelCaseUtil
from module_admin.entity.vo.user_vo import CurrentUserModel


class CustomerService:
    """
    客户管理模块服务层
    """

    @classmethod
    async def get_customer_list_services(cls, query_db: AsyncSession, query_object: CustomerQueryModel):
        """
        获取客户列表

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 客户列表
        """
        customer_list = await CustomerDao.get_customer_list(query_db, query_object)
        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(customer_list)

    @classmethod
    async def get_customer_tree_services(cls, query_db: AsyncSession):
        """
        获取客户树形结构（集团及其下级公司）

        :param query_db: orm对象
        :return: 客户树形结构
        """
        # 获取所有集团级客户（customerLevel='1'表示集团）
        group_query = CustomerQueryModel(customer_level='1')
        groups = await CustomerDao.get_customer_list(query_db, group_query)

        # 获取所有公司级客户，用于后续匹配
        company_query = CustomerQueryModel(customer_level='2')
        all_companies = await CustomerDao.get_customer_list(query_db, company_query)

        # 按集团ID分组公司
        companies_by_group = {}
        for company in all_companies:
            if company.parent_id:
                if company.parent_id not in companies_by_group:
                    companies_by_group[company.parent_id] = []
                companies_by_group[company.parent_id].append(company)

        # 构建树形结构
        tree_data = []

        # 只处理集团级客户作为根节点
        for group in groups:
            # 使用CamelCaseUtil转换集团对象为字典
            group_dict = CamelCaseUtil.transform_result(group)
            group_dict['children'] = []

            # 获取该集团下的所有公司
            group_companies = companies_by_group.get(group.customer_id, [])

            # 使用CamelCaseUtil转换公司对象为字典并添加到集团的children中
            for company in group_companies:
                company_dict = CamelCaseUtil.transform_result(company)
                group_dict['children'].append(company_dict)

            # 无论是否有子公司，都添加集团节点
            tree_data.append(group_dict)

        return tree_data

    @classmethod
    async def get_subordinate_user_ids(cls, query_db: AsyncSession, current_user_id: int):
        """
        获取当前用户的下属员工ID列表

        :param query_db: orm对象
        :param current_user_id: 当前用户ID
        :return: 下属员工ID列表
        """
        # 获取当前用户信息
        current_user_query = select(SysUser).where(
            SysUser.user_id == current_user_id,
            SysUser.del_flag == '0'
        )
        current_user_result = await query_db.execute(current_user_query)
        current_user = current_user_result.scalar_one_or_none()

        if not current_user or not current_user.dept_id:
            return []

        # 获取当前用户所在部门信息
        current_dept_query = select(SysDept).where(
            SysDept.dept_id == current_user.dept_id,
            SysDept.del_flag == '0'
        )
        current_dept_result = await query_db.execute(current_dept_query)
        current_dept = current_dept_result.scalar_one_or_none()

        if not current_dept:
            return []

        # 检查当前用户是否为部门负责人
        if current_dept.leader != current_user_id:
            return []  # 不是部门负责人，返回空列表

        # 获取当前部门及其所有下属部门的ID列表
        dept_ids = [current_user.dept_id]

        # 递归获取所有下属部门
        async def get_child_dept_ids(parent_dept_id):
            child_dept_query = select(SysDept.dept_id).where(
                SysDept.parent_id == parent_dept_id,
                SysDept.del_flag == '0'
            )
            child_dept_result = await query_db.execute(child_dept_query)
            child_dept_ids = [row[0] for row in child_dept_result.fetchall()]

            for child_dept_id in child_dept_ids:
                dept_ids.append(child_dept_id)
                await get_child_dept_ids(child_dept_id)

        await get_child_dept_ids(current_user.dept_id)

        # 获取这些部门下的所有员工ID（排除当前用户）
        subordinate_users_query = select(SysUser.user_id).where(
            SysUser.dept_id.in_(dept_ids),
            SysUser.user_id != current_user_id,
            SysUser.del_flag == '0'
        )
        subordinate_users_result = await query_db.execute(subordinate_users_query)
        subordinate_user_ids = [row[0] for row in subordinate_users_result.fetchall()]

        return subordinate_user_ids

    @classmethod
    async def get_all_customers_page_services(cls, db: AsyncSession, query_params: CustomerPageQueryModel, current_user: CurrentUserModel):
        current_user_id = current_user.user.user_id
        # 获取当前用户的所有下属用户ID列表
        subordinate_user_ids = await cls.get_subordinate_user_ids(db, current_user_id)
        # 将当前用户ID也加入到列表中，以便查询自己负责的客户
        if current_user_id not in subordinate_user_ids:
            subordinate_user_ids.append(current_user_id)
        # 如果没有下属，则只查询自己负责的客户
        if not subordinate_user_ids:
            return PageResponseModel(rows=[], total=0)

        customer_list_result = await CustomerDao.get_customer_page_by_manager_ids(
            db,
            query_params,
            subordinate_user_ids
        )

        return customer_list_result

    @classmethod
    async def get_my_customers_page_services(cls, db: AsyncSession, query_params: CustomerPageQueryModel, current_user: CurrentUserModel):
        # 从current_user中获取当前用户的ID
        # 这里假设current_user是一个包含user_id字段的字典
        # 如果不是这样，你可能需要根据实际情况进行调整
        current_user_id = current_user.user.user_id
        customer_list_result = await CustomerDao.get_customer_page_by_manager_ids(
            db,
            query_params,
            [current_user_id]
        )

        return customer_list_result

    @classmethod
    async def get_customer_page_list_services(cls, query_db: AsyncSession, query_object: CustomerPageQueryModel):
        """
        获取客户分页列表

        :param query_db: orm对象
        :param query_object: 分页查询参数对象
        :return: 客户分页列表
        """
        customer_page = await CustomerDao.get_customer_page_list(query_db, query_object)

        # 获取每个客户的联系人和内部负责人
        for customer in customer_page.rows:
            # 获取联系人
            contacts = await CustomerDao.get_customer_contacts(query_db, customer.get('customerId'))
            # 使用CamelCaseUtil转换联系人对象为字典
            contact_list = CamelCaseUtil.transform_result(contacts)
            customer['contacts'] = contact_list

            # 获取内部负责人
            managers_with_user = await CustomerDao.get_customer_internal_managers(query_db, customer.get('customerId'))
            manager_list = []
            for manager, user in managers_with_user:
                # 使用CamelCaseUtil转换用户对象为字典
                user_dict = CamelCaseUtil.transform_result(user)

                # 使用CamelCaseUtil转换内部负责人对象为字典
                manager_dict = CamelCaseUtil.transform_result(manager)
                manager_dict['user'] = user_dict

                manager_list.append(manager_dict)
            customer['internalManagers'] = manager_list

            # 如果是公司级客户，获取所属集团信息
            if customer.get('customerLevel') == '2' and customer.get('parentId'):
                parent = await CustomerDao.get_customer_by_id(query_db, customer.get('parentId'))
                if parent:
                    # 使用CamelCaseUtil转换父级客户对象为字典
                    parent_dict = CamelCaseUtil.transform_result(parent)
                    customer['parent'] = parent_dict

        return customer_page

    @classmethod
    async def get_customer_detail_services(cls, query_db: AsyncSession, customer_id: int):
        """
        获取客户详情

        :param query_db: orm对象
        :param customer_id: 客户ID
        :return: 客户详情
        """
        customer = await CustomerDao.get_customer_by_id(query_db, customer_id)
        if not customer:
            raise ServiceException(message=f'客户ID：{customer_id}不存在')

        # 使用CamelCaseUtil转换客户对象为字典
        customer_dict = CamelCaseUtil.transform_result(customer)

        # 获取联系人
        contacts = await CustomerDao.get_customer_contacts(query_db, customer.customer_id)
        # 使用CamelCaseUtil转换联系人对象为字典
        contact_list = CamelCaseUtil.transform_result(contacts)
        customer_dict['contacts'] = contact_list

        # 获取内部负责人
        managers_with_user = await CustomerDao.get_customer_internal_managers(query_db, customer.customer_id)
        manager_list = []
        for manager, user in managers_with_user:
            # 使用CamelCaseUtil转换用户对象为字典
            user_dict = CamelCaseUtil.transform_result(user)

            # 使用CamelCaseUtil转换内部负责人对象为字典
            manager_dict = CamelCaseUtil.transform_result(manager)
            manager_dict['user'] = user_dict

            manager_list.append(manager_dict)
        customer_dict['internalManagers'] = manager_list

        # 如果是公司级客户，获取所属集团信息
        if customer.customer_level == '2' and customer.parent_id:
            parent = await CustomerDao.get_customer_by_id(query_db, customer.parent_id)
            if parent:
                # 使用CamelCaseUtil转换父级客户对象为字典
                parent_dict = CamelCaseUtil.transform_result(parent)
                customer_dict['parent'] = parent_dict

        # 获取子客户（如果是集团级客户）
        if customer.customer_level == '1':
            children_query = CustomerQueryModel(parent_id=customer.customer_id, customer_level='2')
            children = await CustomerDao.get_customer_list(query_db, children_query)
            # 使用CamelCaseUtil转换子客户对象为字典
            children_list = CamelCaseUtil.transform_result(children)
            customer_dict['children'] = children_list

        return customer_dict

    @classmethod
    async def check_customer_name_unique_services(cls, query_db: AsyncSession, customer_name: str, customer_id: int = None):
        """
        校验客户名称是否唯一

        :param query_db: orm对象
        :param customer_name: 客户名称
        :param customer_id: 客户ID（编辑时使用）
        :return: 是否唯一
        """
        customer = await CustomerDao.get_customer_by_name(query_db, customer_name)
        if not customer:
            return True
        if customer_id and customer.customer_id == customer_id:
            return True
        return False

    @classmethod
    async def add_customer_services(cls, query_db: AsyncSession, _: Request, customer_model: AddCustomerModel, current_user: dict):
        """
        新增客户

        :param query_db: orm对象
        :param request: 请求对象
        :param customer_model: 新增客户对象
        :param current_user: 当前用户
        :return: 新增结果
        """
        # 校验客户名称是否唯一
        if not await cls.check_customer_name_unique_services(query_db, customer_model.customer_name):
            raise ServiceException(message=f'新增客户{customer_model.customer_name}失败，客户名称已存在')

        # 校验公司级客户必须关联集团
        if customer_model.customer_level == '2' and not customer_model.parent_id:
            raise ServiceException(message='公司级客户必须关联上级集团')

        # 校验上级客户是否存在且为集团级
        if customer_model.parent_id:
            parent = await CustomerDao.get_customer_by_id(query_db, customer_model.parent_id)
            if not parent:
                raise ServiceException(message=f'上级客户ID：{customer_model.parent_id}不存在')
            if parent.customer_level != '1':
                raise ServiceException(message='上级客户必须是集团级客户')

        # 校验至少有一个联系人
        if not customer_model.contacts or len(customer_model.contacts) == 0:
            raise ServiceException(message='至少需要添加一个联系人')

        # 校验至少有一个内部负责人
        if not customer_model.internal_managers or len(customer_model.internal_managers) == 0:
            raise ServiceException(message='至少需要添加一个内部负责人')

        try:
            # 创建客户对象
            customer = Customer()
            customer.customer_name = customer_model.customer_name
            customer.customer_level = customer_model.customer_level
            customer.parent_id = customer_model.parent_id
            customer.customer_type = customer_model.customer_type
            customer.customer_source = customer_model.customer_source
            customer.province = customer_model.province
            customer.city = customer_model.city
            customer.district = customer_model.district
            customer.address = customer_model.address
            customer.status = customer_model.status or '0'
            customer.del_flag = '0'
            customer.create_by = current_user.user.user_name
            customer.create_time = datetime.now()
            customer.remark = customer_model.remark
            customer.customer_importance = customer_model.customer_importance

            # 保存客户
            customer = await CustomerDao.add_customer(query_db, customer)

            # 保存联系人
            for contact_model in customer_model.contacts:
                contact = CustomerContact()
                contact.customer_id = customer.customer_id
                contact.contact_name = contact_model.contact_name
                contact.position = contact_model.position
                contact.phone = contact_model.phone
                contact.wechat = contact_model.wechat
                contact.email = contact_model.email
                contact.is_primary = contact_model.is_primary or '0'
                contact.status = '0'
                contact.create_by = current_user.user.user_name
                contact.create_time = datetime.now()
                contact.remark = contact_model.remark
                await CustomerDao.add_customer_contact(query_db, contact)

            # 保存内部负责人
            for manager_model in customer_model.internal_managers:
                manager = CustomerInternalManager()
                manager.customer_id = customer.customer_id
                manager.user_id = manager_model.user_id
                manager.is_primary = manager_model.is_primary or '0'
                manager.create_by = current_user.user.user_name
                manager.create_time = datetime.now()
                await CustomerDao.add_customer_internal_manager(query_db, manager)

            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def edit_customer_services(cls, query_db: AsyncSession, _: Request, customer_model: EditCustomerModel, current_user: dict):
        """
        编辑客户

        :param query_db: orm对象
        :param request: 请求对象
        :param customer_model: 编辑客户对象
        :param current_user: 当前用户
        :return: 编辑结果
        """
        # 校验客户是否存在
        customer = await CustomerDao.get_customer_by_id(query_db, customer_model.customer_id)
        if not customer:
            raise ServiceException(message=f'客户ID：{customer_model.customer_id}不存在')

        # 校验客户名称是否唯一
        if not await cls.check_customer_name_unique_services(query_db, customer_model.customer_name, customer_model.customer_id):
            raise ServiceException(message=f'修改客户{customer_model.customer_name}失败，客户名称已存在')

        # 校验公司级客户必须关联集团
        if customer_model.customer_level == '2' and not customer_model.parent_id:
            raise ServiceException(message='公司级客户必须关联上级集团')

        # 校验上级客户是否存在且为集团级
        if customer_model.parent_id:
            parent = await CustomerDao.get_customer_by_id(query_db, customer_model.parent_id)
            if not parent:
                raise ServiceException(message=f'上级客户ID：{customer_model.parent_id}不存在')
            if parent.customer_level != '1':
                raise ServiceException(message='上级客户必须是集团级客户')
            # 不能选择自己作为上级
            if parent.customer_id == customer_model.customer_id:
                raise ServiceException(message='不能选择自己作为上级客户')

        # 校验至少有一个联系人
        if not customer_model.contacts or len(customer_model.contacts) == 0:
            raise ServiceException(message='至少需要添加一个联系人')

        # 校验至少有一个内部负责人
        if not customer_model.internal_managers or len(customer_model.internal_managers) == 0:
            raise ServiceException(message='至少需要添加一个内部负责人')

        try:
            # 更新客户信息
            customer.customer_name = customer_model.customer_name
            customer.customer_level = customer_model.customer_level
            customer.parent_id = customer_model.parent_id
            customer.customer_type = customer_model.customer_type
            customer.customer_source = customer_model.customer_source
            customer.province = customer_model.province
            customer.city = customer_model.city
            customer.district = customer_model.district
            customer.address = customer_model.address
            customer.status = customer_model.status
            customer.update_by = current_user.user.user_name
            customer.update_time = datetime.now()
            customer.remark = customer_model.remark
            customer.customer_importance = customer_model.customer_importance

            await CustomerDao.update_customer(query_db, customer)

            # 处理联系人
            # 获取现有联系人
            existing_contacts = await CustomerDao.get_customer_contacts(query_db, customer.customer_id)
            existing_contact_ids = [contact.contact_id for contact in existing_contacts]

            # 新增或更新联系人
            for contact_model in customer_model.contacts:
                if contact_model.contact_id and contact_model.contact_id in existing_contact_ids:
                    # 更新现有联系人
                    contact = next((c for c in existing_contacts if c.contact_id == contact_model.contact_id), None)
                    if contact:
                        contact.contact_name = contact_model.contact_name
                        contact.position = contact_model.position
                        contact.phone = contact_model.phone
                        contact.wechat = contact_model.wechat
                        contact.email = contact_model.email
                        contact.is_primary = contact_model.is_primary
                        contact.update_by = current_user.user.user_name
                        contact.update_time = datetime.now()
                        contact.remark = contact_model.remark
                        await CustomerDao.update_customer_contact(query_db, contact)
                else:
                    # 新增联系人
                    contact = CustomerContact()
                    contact.customer_id = customer.customer_id
                    contact.contact_name = contact_model.contact_name
                    contact.position = contact_model.position
                    contact.phone = contact_model.phone
                    contact.wechat = contact_model.wechat
                    contact.email = contact_model.email
                    contact.is_primary = contact_model.is_primary or '0'
                    contact.status = '0'
                    contact.create_by = current_user.user.user_name
                    contact.create_time = datetime.now()
                    contact.remark = contact_model.remark
                    await CustomerDao.add_customer_contact(query_db, contact)

            # 删除不再需要的联系人
            new_contact_ids = [c.contact_id for c in customer_model.contacts if c.contact_id]
            for contact_id in existing_contact_ids:
                if contact_id not in new_contact_ids:
                    await CustomerDao.delete_customer_contact(query_db, contact_id, current_user.user.user_name)

            # 处理内部负责人
            # 获取现有内部负责人
            existing_managers_with_user = await CustomerDao.get_customer_internal_managers(query_db, customer.customer_id)
            existing_managers = [manager for manager, _ in existing_managers_with_user]
            existing_manager_ids = [manager.id for manager in existing_managers]

            # 新增或更新内部负责人
            for manager_model in customer_model.internal_managers:
                if manager_model.id and manager_model.id in existing_manager_ids:
                    # 更新现有内部负责人
                    manager = next((m for m in existing_managers if m.id == manager_model.id), None)
                    if manager:
                        manager.user_id = manager_model.user_id
                        manager.is_primary = manager_model.is_primary
                        manager.update_by = current_user.user.user_name
                        manager.update_time = datetime.now()
                        await CustomerDao.update_customer_internal_manager(query_db, manager)
                else:
                    # 新增内部负责人
                    manager = CustomerInternalManager()
                    manager.customer_id = customer.customer_id
                    manager.user_id = manager_model.user_id
                    manager.is_primary = manager_model.is_primary or '0'
                    manager.create_by = current_user.user.user_name
                    manager.create_time = datetime.now()
                    await CustomerDao.add_customer_internal_manager(query_db, manager)

            # 删除不再需要的内部负责人
            new_manager_ids = [m.id for m in customer_model.internal_managers if m.id]
            for manager_id in existing_manager_ids:
                if manager_id not in new_manager_ids:
                    await CustomerDao.delete_customer_internal_manager(query_db, manager_id)

            await query_db.commit()
            return CrudResponseModel(is_success=True, message='修改成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_customer_services(cls, query_db: AsyncSession, _: Request, customer_id: int, current_user: dict):
        """
        删除客户

        :param query_db: orm对象
        :param request: 请求对象
        :param customer_id: 客户ID
        :param current_user: 当前用户
        :return: 删除结果
        """
        # 校验客户是否存在
        customer = await CustomerDao.get_customer_by_id(query_db, customer_id)
        if not customer:
            raise ServiceException(message=f'客户ID：{customer_id}不存在')

        # 如果是集团级客户，校验是否有关联的公司级客户
        if customer.customer_level == '1':
            children_query = CustomerQueryModel(parent_id=customer.customer_id, customer_level='2')
            children = await CustomerDao.get_customer_list(query_db, children_query)
            if children and len(children) > 0:
                raise ServiceException(message='该集团下存在关联的公司，无法删除')

        try:
            # 删除客户
            await CustomerDao.delete_customer(query_db, customer_id, current_user.user.user_name)

            await query_db.commit()
            return CrudResponseModel(is_success=True, message='删除成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def search_customers_by_name(cls, query_db: AsyncSession, keyword: str) -> List[Dict[str, Any]]:
        """
        根据客户名称模糊搜索客户列表

        :param query_db: orm对象
        :param keyword: 关键字
        :return: 客户列表
        """
        # 直接查询客户信息
        stmt = select(Customer).where(
            and_(
                Customer.del_flag == '0',
                Customer.customer_name.like(f'%{keyword}%')
            )
        ).order_by(Customer.create_time.desc())
        result = await query_db.execute(stmt)
        customers = result.scalars().all()

        # 构建结果
        result = []
        for customer in customers:
            # 查询客户的主要联系人
            contact_stmt = select(CustomerContact).where(
                and_(
                    CustomerContact.customer_id == customer.customer_id,
                    CustomerContact.status == '0'
                )
            ).order_by(CustomerContact.is_primary.desc(), CustomerContact.create_time.desc())
            contact_result = await query_db.execute(contact_stmt)
            contact = contact_result.scalars().first()

            # 设置联系人信息
            contact_name = ""
            contact_phone = ""
            if contact:
                contact_name = contact.contact_name
                contact_phone = contact.phone

            # 转换为前端期望的数据结构
            result.append({
                "id": customer.customer_id,
                "name": customer.customer_name,
                "address": customer.address,
                "contactName": contact_name,
                "contactPhone": contact_phone
            })
        return result
