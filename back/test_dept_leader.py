#!/usr/bin/env python3
"""
测试检查部门负责人功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import AsyncSessionLocal
from module_customer.service.customer_service import CustomerService
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from module_admin.dao.user_dao import UserDao


async def test_dept_leader():
    """测试检查部门负责人功能"""
    
    async with AsyncSessionLocal() as db:
        print("=== 测试检查部门负责人功能 ===\n")
        
        # 测试用户1（超级管理员）
        print("1. 测试用户1（超级管理员）:")
        test_user_id = 1
        
        user_detail = await UserDao.get_user_detail_by_id(db, test_user_id)
        if user_detail and user_detail.get('user_basic_info'):
            user_info = user_detail['user_basic_info']
            
            current_user = CurrentUserModel(
                permissions=[],
                roles=[],
                user=UserInfoModel(
                    userId=user_info.user_id,
                    deptId=user_info.dept_id,
                    userName=user_info.user_name,
                    nickName=user_info.nick_name,
                    dept=user_detail.get('user_dept_info')
                )
            )
            
            print(f"  用户ID: {current_user.user.user_id}")
            print(f"  用户名: {current_user.user.nick_name}")
            
            try:
                result = await CustomerService.check_user_is_dept_leader_services(db, current_user)
                print(f"  ✅ 检查结果: {result}")
                
                if result['isDeptLeader']:
                    print(f"  📋 负责 {result['leaderDeptCount']} 个部门")
                    print(f"  📋 负责的部门ID: {result['leaderDeptIds']}")
                else:
                    print("  ❌ 不是任何部门的负责人")
                    
            except Exception as e:
                print(f"  ❌ 失败: {e}")
        
        # 测试用户2（年糕）
        print(f"\n2. 测试用户2（年糕）:")
        test_user_id_2 = 2
        
        user_detail_2 = await UserDao.get_user_detail_by_id(db, test_user_id_2)
        if user_detail_2 and user_detail_2.get('user_basic_info'):
            user_info_2 = user_detail_2['user_basic_info']
            
            current_user_2 = CurrentUserModel(
                permissions=[],
                roles=[],
                user=UserInfoModel(
                    userId=user_info_2.user_id,
                    deptId=user_info_2.dept_id,
                    userName=user_info_2.user_name,
                    nickName=user_info_2.nick_name,
                    dept=user_detail_2.get('user_dept_info')
                )
            )
            
            print(f"  用户ID: {current_user_2.user.user_id}")
            print(f"  用户名: {current_user_2.user.nick_name}")
            
            try:
                result2 = await CustomerService.check_user_is_dept_leader_services(db, current_user_2)
                print(f"  ✅ 检查结果: {result2}")
                
                if result2['isDeptLeader']:
                    print(f"  📋 负责 {result2['leaderDeptCount']} 个部门")
                    print(f"  📋 负责的部门ID: {result2['leaderDeptIds']}")
                else:
                    print("  ❌ 不是任何部门的负责人")
                    
            except Exception as e:
                print(f"  ❌ 失败: {e}")
        
        # 查看所有部门负责人信息
        print(f"\n3. 查看所有部门负责人信息:")
        from sqlalchemy import select
        from module_admin.entity.do.dept_do import SysDept
        from module_admin.entity.do.user_do import SysUser
        
        dept_leaders_query = select(SysDept, SysUser).where(
            SysDept.leader == SysUser.user_id,
            SysDept.del_flag == '0',
            SysUser.del_flag == '0'
        ).order_by(SysDept.dept_id)
        
        dept_leaders_result = await db.execute(dept_leaders_query)
        dept_leaders = dept_leaders_result.all()
        
        print(f"  部门负责人总数: {len(dept_leaders)}")
        for dept, user in dept_leaders:
            print(f"    部门: {dept.dept_name}(ID:{dept.dept_id}), 负责人: {user.nick_name}(ID:{user.user_id})")


if __name__ == "__main__":
    asyncio.run(test_dept_leader())
