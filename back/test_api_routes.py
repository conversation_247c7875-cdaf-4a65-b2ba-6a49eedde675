#!/usr/bin/env python3
"""
测试API路由是否正确
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app import app

def test_routes():
    """测试API路由"""

    client = TestClient(app)

    print("=== 测试API路由 ===\n")

    # 测试不需要认证的路由
    print("1. 测试基本路由:")

    # 测试根路径
    try:
        response = client.get("/")
        print(f"  GET / : {response.status_code}")
    except Exception as e:
        print(f"  GET / : 错误 - {e}")

    # 测试健康检查
    try:
        response = client.get("/health")
        print(f"  GET /health : {response.status_code}")
    except Exception as e:
        print(f"  GET /health : 错误 - {e}")

    print("\n2. 测试客户相关路由（无认证）:")

    # 测试客户树形结构（通常需要认证，这里只测试路由是否存在）
    try:
        response = client.get("/customer/tree")
        print(f"  GET /customer/tree : {response.status_code} (预期401未认证)")
    except Exception as e:
        print(f"  GET /customer/tree : 错误 - {e}")

    # 测试检查部门负责人（通常需要认证）
    try:
        response = client.get("/customer/check-dept-leader")
        print(f"  GET /customer/check-dept-leader : {response.status_code} (预期401未认证)")
    except Exception as e:
        print(f"  GET /customer/check-dept-leader : 错误 - {e}")

    # 测试客户详情（应该被路由到正确的处理器）
    try:
        response = client.get("/customer/123")
        print(f"  GET /customer/123 : {response.status_code} (预期401未认证)")
    except Exception as e:
        print(f"  GET /customer/123 : 错误 - {e}")

    # 测试客户名称检查
    try:
        response = client.get("/customer/check/test-name")
        print(f"  GET /customer/check/test-name : {response.status_code} (预期401未认证)")
    except Exception as e:
        print(f"  GET /customer/check/test-name : 错误 - {e}")

    print("\n3. 查看所有客户相关路由:")

    # 获取所有路由
    routes = []
    for route in app.routes:
        if hasattr(route, 'path') and '/customer' in route.path:
            methods = getattr(route, 'methods', ['GET'])
            routes.append(f"  {list(methods)[0] if methods else 'GET'} {route.path}")

    if routes:
        print("  客户相关路由:")
        for route in sorted(routes):
            print(route)
    else:
        print("  未找到客户相关路由")

if __name__ == "__main__":
    test_routes()
